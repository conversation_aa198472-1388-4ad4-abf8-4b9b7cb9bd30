export interface User {
  id: string
  email: string
  username: string
  full_name?: string
  profile_picture?: string
  role: string
  auth_provider: string
  tenant_id: string
  tenant_label: string
  tenant_slug: string
  access_token?: string
  phone_number?: string
  country_code?: string
  last_login?: string
  previous_login?: string
  onboarding_completed?: boolean
}

export interface LoginCredentials {
  email: string
  password: string
  client_id?: string
}

export interface SignupCredentials {
  username: string
  email: string
  password: string
  full_name?: string
  client_id: string
}

export interface GoogleAuthCredentials {
  id_token: string
  client_id: string
}

export interface OnboardingData {
  age: number
  difficulty_level: number // 1 = easy, 2 = medium, 3 = hard
  preferred_topics?: string[]
  character_name?: string
  character_gender?: 'male' | 'female'
  character_type?: string
}

export interface LoginResponse {
  id: string
  access_token: string
  token_type: string
  username: string
  email: string
  role: string
  tenant_id: string
  tenant_label: string
  tenant_slug: string
  full_name?: string
  profile_picture?: string
  auth_provider: string
  last_login?: string
  previous_login?: string
  phone_number?: string
  country_code?: string
  onboarding_completed?: boolean
}

export interface SignupResponse extends LoginResponse {
  // Signup now returns the same format as login for auto-login
  // All fields from LoginResponse are inherited
}

export interface OnboardingResponse {
  success: boolean
  message?: string
  user_id?: string
  personalization_ready?: boolean
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>
  signup: (credentials: SignupCredentials) => Promise<void>
  googleAuth: (credentials: GoogleAuthCredentials) => Promise<void>
  submitOnboarding: (data: OnboardingData) => Promise<void>
  logout: () => void
  clearError: () => void
}
