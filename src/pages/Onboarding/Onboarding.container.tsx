import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../../store/hooks'
import { submitOnboardingData, clearError } from '../../store/slices/authSlice'
import OnboardingComponent from './Onboarding.component'
import { OnboardingContainerProps, OnboardingFormData, OnboardingFormErrors } from './types'

/**
 * Onboarding Container - Handles all logic, state, and side effects
 */
const OnboardingContainer: React.FC<OnboardingContainerProps> = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { user, isAuthenticated, isLoading, error } = useAppSelector((state) => state.auth)

  // Form state
  const [formData, setFormData] = useState<OnboardingFormData>({
    age: 7,
    difficulty_level: 1, // Default to easy
    preferred_topics: [],
    character_name: '',
    gender: null,
  })

  const [formErrors, setFormErrors] = useState<OnboardingFormErrors>({})

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Redirect if onboarding already completed
  useEffect(() => {
    if (user?.onboarding_completed) {
      navigate('/dashboard', { replace: true })
    }
  }, [user, navigate])

  // Clear errors when auth error changes
  useEffect(() => {
    if (error) {
      setFormErrors(prev => ({ ...prev, general: error }))
    }
  }, [error])

  // Handle input changes
  const handleInputChange = (field: keyof OnboardingFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear field-specific error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }))
    }
    
    // Clear general error when user starts typing
    if (formErrors.general) {
      setFormErrors(prev => ({ ...prev, general: undefined }))
      dispatch(clearError())
    }
  }

  // Validate form
  const validateForm = (): boolean => {
    const errors: OnboardingFormErrors = {}

    if (!formData.age || formData.age < 3 || formData.age > 18) {
      errors.age = 'Please enter a valid age between 3 and 18'
    }

    if (!formData.difficulty_level || formData.difficulty_level < 1 || formData.difficulty_level > 3) {
      errors.difficulty_level = 'Please select a difficulty level'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      await dispatch(submitOnboardingData({
        age: formData.age,
        difficulty_level: formData.difficulty_level,
        preferred_topics: formData.preferred_topics,
      })).unwrap()

      // Navigate to dashboard page after successful onboarding
      navigate('/dashboard', { replace: true })
    } catch (error) {
      // Error handling is done in Redux slice
      console.error('Onboarding failed:', error)
    }
  }

  // Handle clear error
  const handleClearError = () => {
    setFormErrors(prev => ({ ...prev, general: undefined }))
    dispatch(clearError())
  }

  return (
    <OnboardingComponent
      formData={formData}
      errors={formErrors}
      isLoading={isLoading}
      onInputChange={handleInputChange}
      onSubmit={handleSubmit}
      onClearError={handleClearError}
    />
  )
}

export default OnboardingContainer
